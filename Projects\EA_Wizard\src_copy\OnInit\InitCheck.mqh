#property strict

#include "../../module/EAPipeline/EAPipeline.mqh"

// 初始化檢查階段
class InitCheck : public EAPipeline
{
public:
    InitCheck() : EAPipeline(ONINIT_START, "InitCheck"){}
    void Execute() override { // 實現 Execute 方法
        // Print("初始化檢查階段開始");

        EAFileLog* logger = dynamic_cast<EAFileLog*>(GetItem("EALogger").GetValue());

        int maxRetries = 4;
        int retryCount = 0;

        while((!IsTradeAllowed() || !IsConnected()) && retryCount < maxRetries) {
            if(!IsTradeAllowed())
                // Print("交易不允許");
                logger.Error("交易不允許");
            
            if(!IsConnected())

                logger.Error("未連接到交易伺服器");
            
            Sleep(5000);
            retryCount++;
        }

        if(retryCount >= maxRetries) {
            // Print("初始化檢查階段失敗，超過最大重試次數");
            Alert("初始化檢查階段失敗，超過最大重試次數");
            SetResult(false, "初始化檢查階段失敗，超過最大重試次數", "InitCheck");
            return;
        }

        // Print("初始化檢查階段結束");
    }
}init_check_stage;
