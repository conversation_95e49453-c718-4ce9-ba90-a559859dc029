//+------------------------------------------------------------------+
//|                                   EAErrorHandlingRegistryTest.mq4 |
//|                                                        EA_Wizard |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "../module/EAPipeline/components/decorator/EAErrorHandlingRegistry.mqh"

//+------------------------------------------------------------------+
//| 測試 EAErrorHandlingRegistry 類                                   |
//+------------------------------------------------------------------+
void OnStart()
{
    // 創建日誌記錄器
    EAFileLog* logger = EAFileLog::GetInstance();
    logger.Log(INFO, "開始測試 EAErrorHandlingRegistry 類");
    
    // 創建錯誤處理器
    EAErrorHandler* error_handler = EAErrorHandler::GetInstance();
    
    // 創建基本註冊器
    EARegistry* registry = new EARegistry("TestRegistry", "TestRegistry", 10);
    
    // 創建錯誤處理裝飾器
    EAErrorHandlingRegistry* error_registry = new EAErrorHandlingRegistry(registry, "TestErrorRegistry", error_handler, logger);
    
    // 測試註冊項目
    logger.Log(INFO, "測試註冊項目");
    
    // 測試正常註冊
    void* value1 = new int(123);
    RegistryResult<string>* result1 = error_registry.Register("Item1", "測試項目1", value1);
    if(result1.IsSuccess())
    {
        logger.Log(INFO, "註冊項目1成功，鍵: " + result1.GetKey());
    }
    else
    {
        logger.Log(ERROR, "註冊項目1失敗: " + result1.GetMessage());
    }
    
    // 測試空名稱註冊
    void* value2 = new int(456);
    RegistryResult<string>* result2 = error_registry.Register("", "測試項目2", value2);
    if(result2.IsSuccess())
    {
        logger.Log(INFO, "註冊項目2成功，鍵: " + result2.GetKey());
    }
    else
    {
        logger.Log(ERROR, "註冊項目2失敗: " + result2.GetMessage());
    }
    
    // 測試重複註冊
    void* value3 = new int(789);
    RegistryResult<string>* result3 = error_registry.Register("Item1", "測試項目3", value3);
    if(result3.IsSuccess())
    {
        logger.Log(INFO, "註冊項目3成功，鍵: " + result3.GetKey());
    }
    else
    {
        logger.Log(ERROR, "註冊項目3失敗: " + result3.GetMessage());
    }
    
    // 測試獲取項目
    logger.Log(INFO, "測試獲取項目");
    
    // 測試正常獲取
    RegistryItem<void*>* item1 = error_registry.GetItem("Item1");
    if(item1 != NULL)
    {
        int* value = (int*)item1.GetValue();
        logger.Log(INFO, "獲取項目1成功，值: " + IntegerToString(*value));
    }
    else
    {
        logger.Log(ERROR, "獲取項目1失敗");
    }
    
    // 測試獲取不存在的項目
    RegistryItem<void*>* item2 = error_registry.GetItem("NonExistentItem");
    if(item2 != NULL)
    {
        int* value = (int*)item2.GetValue();
        logger.Log(INFO, "獲取不存在項目成功，值: " + IntegerToString(*value));
    }
    else
    {
        logger.Log(ERROR, "獲取不存在項目失敗");
    }
    
    // 測試獲取空鍵項目
    RegistryItem<void*>* item3 = error_registry.GetItem("");
    if(item3 != NULL)
    {
        int* value = (int*)item3.GetValue();
        logger.Log(INFO, "獲取空鍵項目成功，值: " + IntegerToString(*value));
    }
    else
    {
        logger.Log(ERROR, "獲取空鍵項目失敗");
    }
    
    // 測試移除項目
    logger.Log(INFO, "測試移除項目");
    
    // 測試正常移除
    bool unregister1 = error_registry.Unregister("Item1");
    if(unregister1)
    {
        logger.Log(INFO, "移除項目1成功");
    }
    else
    {
        logger.Log(ERROR, "移除項目1失敗");
    }
    
    // 測試移除不存在的項目
    bool unregister2 = error_registry.Unregister("NonExistentItem");
    if(unregister2)
    {
        logger.Log(INFO, "移除不存在項目成功");
    }
    else
    {
        logger.Log(ERROR, "移除不存在項目失敗");
    }
    
    // 測試移除空鍵項目
    bool unregister3 = error_registry.Unregister("");
    if(unregister3)
    {
        logger.Log(INFO, "移除空鍵項目成功");
    }
    else
    {
        logger.Log(ERROR, "移除空鍵項目失敗");
    }
    
    // 測試其他功能
    logger.Log(INFO, "測試其他功能");
    
    // 測試獲取項目數量
    int count = error_registry.GetCount();
    logger.Log(INFO, "註冊器項目數量: " + IntegerToString(count));
    
    // 測試獲取所有鍵
    string keys[];
    int key_count = error_registry.GetAllKeys(keys);
    logger.Log(INFO, "獲取到 " + IntegerToString(key_count) + " 個鍵");
    for(int i = 0; i < key_count; i++)
    {
        logger.Log(INFO, "鍵 " + IntegerToString(i) + ": " + keys[i]);
    }
    
    // 測試清空註冊器
    error_registry.Clear();
    count = error_registry.GetCount();
    logger.Log(INFO, "清空後註冊器項目數量: " + IntegerToString(count));
    
    // 釋放資源
    delete error_registry;
    delete registry;
    
    logger.Log(INFO, "測試 EAErrorHandlingRegistry 類完成");
}
