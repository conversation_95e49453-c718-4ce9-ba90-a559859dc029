//+------------------------------------------------------------------+
//|                                                   StandardEA.mq4 |
//|                                                    EA_Wizard     |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard"
#property link      ""
#property version   "1.00"
#property strict

#include "../module/EAPipeline/EAPipelineDriver.mqh"
#include "OnInit/index.mqh"
#include "OnTick/index.mqh"
#include "OnDeinit/index.mqh"

//--- 輸入參數
input double   Lots = 0.1;           // 交易手數
input int      StopLoss = 100;       // 止損點數
input int      TakeProfit = 200;     // 獲利點數
input int      MagicNumber = 12345;  // 魔術數字

int OnInit()
{
   // 初始化代碼
   ENUM_INIT_RETCODE result = EAPipelineDriver::GetInstance().OnInit();

   return(result);
}

void OnDeinit(const int reason)
{
   // 執行清理代碼
   EAPipelineDriver::GetInstance().OnDeinit(reason);
}

void OnTick()
{
   // 執行 EA 處理器
   EAPipelineDriver::GetInstance().OnTick();
}

//+------------------------------------------------------------------+
//| 自定義函數                                                        |
//+------------------------------------------------------------------+
// 您可以在這裡添加自定義函數
