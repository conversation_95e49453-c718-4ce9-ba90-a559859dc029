#property strict

#include "../IValidator.mqh"
#include "../ValidationConfig.mqh"
#include "../../mql4-lib-master/Collection/Vector.mqh"

//+------------------------------------------------------------------+
//| 列表驗證器                                                         |
//| 用於驗證列表大小是否在指定範圍內，並可選擇性地驗證列表中的每個元素       |
//+------------------------------------------------------------------+
template<typename T>
class CListValidator : public CBaseValidator
{
private:
    T m_item;                 // 要驗證的項目
    Vector<T>* m_list;        // 要驗證的列表
    string m_field;           // 欄位名稱
    IValidator* m_itemValidator; // 用於驗證列表項的驗證器（可選）
    bool m_ownItemValidator;  // 是否擁有項目驗證器的所有權
    
public:
    // 建構函數 - 基本列表驗證
    CListValidator(T item, Vector<T>* list, const string field)
        : CBaseValidator(VALIDATOR_NAME_LIST, VALIDATOR_TYPE_LIST)
        , m_list(list)
        , m_item(item)
        , m_field(field)
        , m_itemValidator(NULL)
        , m_ownItemValidator(false)
    {}
    
    // 建構函數 - 帶項目驗證器
    CListValidator(T item, Vector<T>* list, const string field, IValidator* itemValidator, bool ownItemValidator = true)
        : CBaseValidator(VALIDATOR_NAME_LIST, VALIDATOR_TYPE_LIST)
        , m_list(list)
        , m_item(item)
        , m_field(field)
        , m_itemValidator(itemValidator)
        , m_ownItemValidator(ownItemValidator)
    {}
    
    // 解構函數
    ~CListValidator()
    {
        if(m_itemValidator != NULL && m_ownItemValidator)
        {
            delete m_itemValidator;
            m_itemValidator = NULL;
        }
    }
    
    // 執行驗證
    virtual CValidationResult* Validate() override
    {
        CValidationResult* result = new CValidationResult();
        
        // 檢查列表是否為空
        if(m_list == NULL)
        {
            result.SetInvalid(
                m_field, 
                StringFormat(VALIDATOR_MSG_REQUIRED, m_field),
                VALIDATOR_SOURCE_DEFAULT,
                VALIDATOR_ERROR_REQUIRED
            );
            return result;
        }

        // 檢查列表是否為空
        if(m_list.size() == 0)
        {
            result.SetInvalid(
                m_field, 
                StringFormat(VALIDATOR_MSG_REQUIRED, m_field),
                VALIDATOR_SOURCE_DEFAULT,
                VALIDATOR_ERROR_REQUIRED
            );
            return result;
        }

        // 檢查列表中是否包含指定的項目
        if(m_itemValidator == NULL)
        {
            for(int i = 0; i < m_list.size(); i++)
            {
                if(m_item == m_list.get(i))
                {
                    break;
                }
            }
        }
        
        // 如果有項目驗證器，則驗證每個項目
        if(m_itemValidator != NULL)
        {
            for(int i = 0; i < m_list.size(); i++)
            {
                // 這裡需要根據項目類型進行適當的處理
                // 注意：這裡假設項目驗證器可以處理列表中的項目類型
                // 實際使用時可能需要根據具體情況調整
                
                // 執行項目驗證
                CValidationResult* itemResult = m_itemValidator.Validate();
                if(!itemResult.IsValid())
                {
                    string errorMsg = StringFormat(
                        VALIDATOR_MSG_LIST_ITEM, 
                        i, 
                        itemResult.GetMessage()
                    );
                    
                    result.SetInvalid(
                        m_field, 
                        errorMsg,
                        VALIDATOR_SOURCE_DEFAULT,
                        VALIDATOR_ERROR_FORMAT
                    );
                    
                    delete itemResult;
                    return result;
                }
                
                delete itemResult;
            }
        }
        
        return result;
    }

    // 設置要驗證的項目
    void SetItem(T item)
    {
        m_item = item;
    }
    
    // 獲取要驗證的項目
    T GetItem() const
    {
        return m_item;
    }
    
    // 設置要驗證的列表
    void SetList(Vector<T>* list)
    {
        m_list = list;
    }
    
    // 獲取要驗證的列表
    Vector<T>* GetList() const
    {
        return m_list;
    }

    // 設置欄位名稱
    void SetField(const string field)
    {
        m_field = field;
    }
    
    // 獲取欄位名稱
    string GetField() const
    {
        return m_field;
    }
    
    // 設置項目驗證器
    void SetItemValidator(IValidator* validator, bool ownValidator = true)
    {
        // 如果已有驗證器且擁有其所有權，則先刪除
        if(m_itemValidator != NULL && m_ownItemValidator)
        {
            delete m_itemValidator;
        }
        
        m_itemValidator = validator;
        m_ownItemValidator = ownValidator;
    }
    
    // 獲取項目驗證器
    IValidator* GetItemValidator() const
    {
        return m_itemValidator;
    }
};
