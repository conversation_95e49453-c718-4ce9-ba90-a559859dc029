#property strict

#include <../../../src/mql4-lib-master/Collection/Vector.mqh>
#include "../Validators/ValidatorCollection.mqh"

//+------------------------------------------------------------------+
//| 列表驗證器使用示例                                                  |
//+------------------------------------------------------------------+

// 示例1：基本列表大小驗證
void ListValidatorExample1()
{
    // 創建一個字符串列表
    Vector<string>* stringList = new Vector<string>();
    stringList.add("EURUSD");
    stringList.add("GBPUSD");
    stringList.add("USDJPY");
    
    // 創建列表驗證器 - 檢查列表大小是否在2到5之間
    CListValidator<string>* listValidator = CValidatorFactory::CreateListValidator(
        stringList, 2, 5, "SymbolList"
    );
    
    // 執行驗證
    CValidationResult* result = listValidator.Validate();
    
    // 輸出驗證結果
    Print("列表大小驗證結果: ", result.IsValid() ? "通過" : "失敗");
    if(!result.IsValid())
    {
        Print("錯誤訊息: ", result.GetMessage());
    }
    
    // 清理資源
    delete result;
    delete listValidator;
    delete stringList;
}

// 示例2：帶項目驗證器的列表驗證
void ListValidatorExample2()
{
    // 創建一個字符串列表
    Vector<string>* symbolList = new Vector<string>();
    symbolList.add("EURUSD");
    symbolList.add("GBPUSD");
    symbolList.add("");  // 空字符串，將導致驗證失敗
    
    // 創建項目驗證器 - 檢查每個項目是否為必填
    CRequiredValidator* itemValidator = CValidatorFactory::CreateRequiredValidator("", "Symbol");
    
    // 創建列表驗證器 - 檢查列表大小和每個項目
    CListValidator<string>* listValidator = CValidatorFactory::CreateListValidator(
        symbolList, 1, 10, "SymbolList", itemValidator
    );
    
    // 執行驗證
    CValidationResult* result = listValidator.Validate();
    
    // 輸出驗證結果
    Print("列表項目驗證結果: ", result.IsValid() ? "通過" : "失敗");
    if(!result.IsValid())
    {
        Print("錯誤訊息: ", result.GetMessage());
    }
    
    // 清理資源
    delete result;
    delete listValidator;  // 會自動刪除 itemValidator
    delete symbolList;
}

// 示例3：組合驗證器中使用列表驗證器
void ListValidatorExample3()
{
    // 創建一個數值列表
    Vector<double>* priceList = new Vector<double>();
    priceList.add(1.2345);
    priceList.add(2.3456);
    priceList.add(3.4567);
    
    // 創建列表驗證器
    CListValidator<double>* listValidator = CValidatorFactory::CreateListValidator(
        priceList, 1, 5, "PriceList"
    );
    
    // 創建組合驗證器
    CValidatorComposite* composite = new CValidatorComposite();
    composite.Add(listValidator);
    
    // 添加其他驗證器
    CRequiredValidator* requiredValidator = CValidatorFactory::CreateRequiredValidator("EURUSD", "Symbol");
    composite.Add(requiredValidator);
    
    // 執行所有驗證
    CValidationResult* result = composite.Validate();
    
    // 輸出驗證結果
    Print("組合驗證結果: ", result.IsValid() ? "通過" : "失敗");
    if(!result.IsValid())
    {
        Print("錯誤訊息: ", result.GetMessage());
    }
    
    // 清理資源
    delete result;
    delete composite;  // 會自動刪除所有添加的驗證器
    delete priceList;
}

// 示例4：在驗證組中使用列表驗證器
void ListValidatorExample4()
{
    // 創建一個整數列表
    Vector<int>* lotSizeList = new Vector<int>();
    lotSizeList.add(1);
    lotSizeList.add(2);
    lotSizeList.add(5);
    
    // 創建驗證組
    CValidationGroup* validationGroup = new CValidationGroup("ConfigValidation");
    
    // 創建列表驗證器並添加到驗證組
    CListValidator<int>* listValidator = CValidatorFactory::CreateListValidator(
        lotSizeList, 1, 10, "LotSizeList"
    );
    validationGroup.AddValidator(listValidator);
    
    // 添加其他驗證器
    CStringValidator* stringValidator = CValidatorFactory::CreateStringValidator(
        "EURUSD", 3, 10, "Symbol"
    );
    validationGroup.AddValidator(stringValidator);
    
    // 執行所有驗證
    CValidationResult* result = validationGroup.ValidateAll();
    
    // 輸出驗證結果
    Print("驗證組結果: ", result.IsValid() ? "通過" : "失敗");
    if(!result.IsValid())
    {
        Print("錯誤訊息: ", result.GetMessage());
    }
    
    // 清理資源
    delete result;
    delete validationGroup;  // 會自動刪除所有添加的驗證器
    delete lotSizeList;
}
